<?php

namespace app\admin\controller\news;

use app\common\controller\Backend;
use app\common\library\Auth;
use think\Db;

/**
 * 新闻管理
 *
 * @icon fa fa-newspaper-o
 */
class News extends Backend
{
    protected $relationSearch = true;
    protected $searchFields = 'id,title';

    /**
     * @var \app\admin\model\News
     */
    protected $model = null;

    public function _initialize()
    {
        parent::_initialize();
        $this->model = new \app\admin\model\News;
    }

    /**
     * 查看
     */
    public function index()
    {
        //设置过滤方法
        $this->request->filter(['strip_tags', 'trim']);
        if ($this->request->isAjax()) {
            //如果发送的来源是Selectpage，则转发到Selectpage
            if ($this->request->request('keyField')) {
                return $this->selectpage();
            }
            list($where, $sort, $order, $offset, $limit) = $this->buildparams();
            $list = $this->model
                ->where($where)
                ->order($sort, $order)
                ->paginate($limit);
            $result = array("total" => $list->total(), "rows" => $list->items());

            return json($result);
        }
        
        // 获取导航数据
        list($menulist, $navlist, $fixedmenu, $referermenu) = $this->auth->getSidebar([
            'dashboard' => 'hot',
            'addon'     => ['new', 'red', 'badge'],
            'auth/rule' => __('Menu'),
        ], $this->view->site['fixedpage']);
        
        // 分配到模板
        $this->view->assign([
            'menulist' => $menulist,
            'navlist' => $navlist,
            'fixedmenu' => $fixedmenu,
            'referermenu' => $referermenu
        ]);
        
        return $this->view->fetch();
    }

    /**
     * 添加
     */
    public function add()
    {
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);

                if ($this->dataLimit && $this->dataLimitFieldAutoFill) {
                    $params[$this->dataLimitField] = $this->auth->id;
                }
                
                // 获取分类信息
                $category = Db::name('news_category')
                    ->where('id', $params['category_id'])
                    ->field('id, keywords')
                    ->find();
                    
                if ($category) {
                    $params['keywords'] = $category['keywords'];
                }

                // 获取选中的标签
                $tagIds = isset($params['tags']) ? $params['tags'] : [];
                
                // 获取标签名称并组合成字符串
                $tagNames = [];
                if (!empty($tagIds)) {
                    $tagNames = Db::name('news_tags')
                        ->where('id', 'in', $tagIds)
                        ->column('name');
                }
                
                // 将标签名称保存到params中
                $params['tags'] = !empty($tagNames) ? implode(',', $tagNames) : '';

                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.add' : $name) : $this->modelValidate;
                        $this->model->validateFailException(true)->validate($validate);
                    }
                    
                    // 保存新闻
                    $result = $this->model->allowField(true)->save($params);
                    
                    // 获取新插入的新闻ID
                    $newsId = $this->model->id;
                    
                    // 保存标签关联
                    if ($newsId && !empty($tagIds)) {
                        $relationData = [];
                        foreach ($tagIds as $tagId) {
                            $relationData[] = [
                                'news_id' => $newsId,
                                'tag_id' => $tagId,
                                'createtime' => time()
                            ];
                        }
                        Db::name('news_tags_relation')->insertAll($relationData);
                    }
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were inserted'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 获取所有分类
        $categoryList = Db::name('news_category')
            ->where('status', 1)
            ->field('id, name, keywords')
            ->select();

        // 获取所有标签
        $tags = Db::name('news_tags')
            ->where('status', 1)
            ->field('id, name')
            ->select();

        $this->view->assign([
            'categoryList' => $categoryList,
            'tags' => $tags
        ]);
        return $this->view->fetch();
    }

    /**
     * 编辑
     */
    public function edit($ids = null)
    {
        $row = $this->model->get($ids);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $adminIds = $this->getDataLimitAdminIds();
        if (is_array($adminIds)) {
            if (!in_array($row[$this->dataLimitField], $adminIds)) {
                $this->error(__('You have no permission'));
            }
        }
        if ($this->request->isPost()) {
            $params = $this->request->post("row/a");
            if ($params) {
                $params = $this->preExcludeFields($params);
                $result = false;
                Db::startTrans();
                try {
                    //是否采用模型验证
                    if ($this->modelValidate) {
                        $name = str_replace("\\model\\", "\\validate\\", get_class($this->model));
                        $validate = is_bool($this->modelValidate) ? ($this->modelSceneValidate ? $name . '.edit' : $name) : $this->modelValidate;
                        $row->validateFailException(true)->validate($validate);
                    }

                    // 获取选中的标签
                    $tagIds = $this->request->post('tags/a');
                    
                    // 获取标签名称并组合成字符串
                    $tagNames = [];
                    if (!empty($tagIds)) {
                        $tagNames = Db::name('news_tags')
                            ->where('id', 'in', $tagIds)
                            ->column('name');
                    }
                    
                    // 清空原有数据
                    $row->tags = '';
                    $row->save();
                    Db::name('news_tags_relation')->where('news_id', $ids)->delete();
                    
                    // 更新news表中的tags字段
                    if (!empty($tagNames)) {
                        $params['tags'] = implode(',', $tagNames);
                    }
                    
                    // 保存新闻数据
                    $result = $row->allowField(true)->save($params);
                    
                    // 插入新的标签关联
                    if (!empty($tagIds)) {
                        $relationData = [];
                        foreach ($tagIds as $tagId) {
                            $relationData[] = [
                                'news_id' => $ids,
                                'tag_id' => $tagId,
                                'createtime' => time()
                            ];
                        }
                        Db::name('news_tags_relation')->insertAll($relationData);
                    }
                    
                    Db::commit();
                } catch (ValidateException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (PDOException $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                } catch (Exception $e) {
                    Db::rollback();
                    $this->error($e->getMessage());
                }
                if ($result !== false) {
                    $this->success();
                } else {
                    $this->error(__('No rows were updated'));
                }
            }
            $this->error(__('Parameter %s can not be empty', ''));
        }

        // 获取所有分类
        $categoryList = Db::name('news_category')
            ->where('status', 1)
            ->field('id, name')
            ->select();

        // 获取所有标签
        $tags = Db::name('news_tags')
            ->where('status', 1)
            ->field('id, name')
            ->select();

        // 获取当前新闻的标签
        $selectedTags = $row->getTags();

        $this->view->assign([
            'row' => $row,
            'categoryList' => $categoryList,
            'tags' => $tags,
            'selectedTags' => $selectedTags
        ]);
        return $this->view->fetch();
    }

    /**
     * 删除
     */
    public function del($ids = "")
    {
        if (!$this->request->isPost()) {
            $this->error(__("Invalid parameters"));
        }
        $ids = $ids ? $ids : $this->request->post("ids");
        $row = $this->model->get($ids);
        $this->modelValidate = true;
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        $this->success();
    }

    /**
     * 设置置顶状态
     */
    public function settop()
    {
        $id = $this->request->param('ids');
        $value = $this->request->param('is_top');
        $value = $value ? 1 : 0;
        
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $row->is_top = $value;
        $row->save();
        $this->success();
    }

    /**
     * 设置热门状态
     */
    public function sethot()
    {
        $id = $this->request->param('ids');
        $value = $this->request->param('is_hot');
        $value = $value ? 1 : 0;
        
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $row->is_hot = $value;
        $row->save();
        $this->success();
    }

    /**
     * 设置状态
     */
    public function setstatus()
    {
        $id = $this->request->param('ids');
        $status = $this->request->param('status');
        
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $row->status = $status;
        $row->save();
        $this->success();
    }

    /**
     * 设置推荐状态
     */
    public function setrecommend()
    {
        $id = $this->request->param('ids');
        $value = $this->request->param('is_recommend');
        $value = $value ? 1 : 0;
        
        $row = $this->model->get($id);
        if (!$row) {
            $this->error(__('No Results were found'));
        }
        
        $row->is_recommend = $value;
        $row->save();
        $this->success();
    }
}
