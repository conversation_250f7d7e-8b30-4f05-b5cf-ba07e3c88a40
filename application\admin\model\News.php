<?php

namespace app\admin\model;

use think\Model;

class News extends Model
{
    // 表名
    protected $name = 'news';
    // 自动写入时间戳字段
    protected $autoWriteTimestamp = 'int';
    // 定义时间戳字段名
    protected $createTime = 'createtime';
    protected $updateTime = 'updatetime';
    
    // 追加属性
    protected $append = [
        'status_text',
        'is_top_text',
        'is_recommend_text'
    ];

    // 定义允许被写入的字段
    protected $allowField = ['category_id', 'title', 'description', 'content', 'image', 'keywords', 'tags', 'weigh', 'is_top', 'is_recommend', 'status'];

    public function getOriginData()
    {
        return $this->origin;
    }

    public function getStatusList()
    {
        return ['0' => __('Disabled'), '1' => __('Normal')];
    }

    public function getIsTopList()
    {
        return ['0' => __('No'), '1' => __('Yes')];
    }

    public function getIsRecommendList()
    {
        return ['0' => __('No'), '1' => __('Yes')];
    }

    public function getStatusTextAttr($value, $data)
    {
        $value = $value ? $value : $data['status'];
        $list = $this->getStatusList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getIsTopTextAttr($value, $data)
    {
        $value = $value ? $value : $data['is_top'];
        $list = $this->getIsTopList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    public function getIsRecommendTextAttr($value, $data)
    {
        $value = $value ? $value : $data['is_recommend'];
        $list = $this->getIsRecommendList();
        return isset($list[$value]) ? $list[$value] : '';
    }

    protected function setCreatetimeAttr($value)
    {
        return $value && !is_numeric($value) ? strtotime($value) : $value;
    }

    protected function setUpdatetimeAttr($value)
    {
        return $value && !is_numeric($value) ? strtotime($value) : $value;
    }

    protected function setPublishtimeAttr($value)
    {
        return $value && !is_numeric($value) ? strtotime($value) : $value;
    }

    protected static function init()
    {
        self::beforeInsert(function ($model) {
            if (empty($model->publishtime)) {
                $model->publishtime = $model->createtime;
            }
        });

        // 新增后
        self::afterInsert(function ($model) {
            if (isset($_POST['tags']) && is_array($_POST['tags'])) {
                $tagIds = $_POST['tags'];
                foreach ($tagIds as $tagId) {
                    \think\Db::name('news_tags_relation')->insert([
                        'news_id' => $model->id,
                        'tag_id' => $tagId,
                        'createtime' => time()
                    ]);
                }
            }
        });

        // 更新后
        self::afterUpdate(function ($model) {
            // 移除标签关联处理，因为已经在控制器中处理了
        });

        // 删除后
        self::afterDelete(function ($model) {
            // 删除关联的标签
            \think\Db::name('news_tags_relation')->where('news_id', $model->id)->delete();
        });
    }

    /**
     * 获取新闻的标签
     */
    public function getTags()
    {
        return \think\Db::name('news_tags')
            ->alias('t')
            ->join('news_tags_relation r', 'r.tag_id = t.id')
            ->where('r.news_id', $this->id)
            ->where('t.status', 1)
            ->column('t.id');
    }
} 