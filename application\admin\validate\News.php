<?php

namespace app\admin\validate;

use think\Validate;

class News extends Validate
{
    /**
     * 验证规则
     */
    protected $rule = [
        'title' => 'require|length:1,100',
        'description' => 'length:0,255',
        'content' => 'require',
        'author' => 'length:0,50',
        'image' => 'length:0,255',
        'category_id' => 'require|number',
        'status' => 'require|in:normal,hidden'
    ];

    /**
     * 提示消息
     */
    protected $message = [
        'title.require' => '标题不能为空',
        'title.length' => '标题长度限制在1-100个字符',
        'description.length' => '描述长度限制在255个字符以内',
        'content.require' => '内容不能为空',
        'author.length' => '作者长度限制在50个字符以内',
        'image.length' => '图片路径长度限制在255个字符以内',
        'category_id.require' => '分类不能为空',
        'category_id.number' => '分类ID必须是数字',
        'status.require' => '状态不能为空',
        'status.in' => '状态值不正确'
    ];

    /**
     * 验证场景
     */
    protected $scene = [
        'add'  => ['title', 'description', 'content', 'author', 'image', 'category_id', 'status'],
        'edit' => ['title', 'description', 'content', 'author', 'image', 'category_id', 'status'],
    ];
} 