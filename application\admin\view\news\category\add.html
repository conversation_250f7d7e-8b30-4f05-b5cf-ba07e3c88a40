<div class="panel panel-default panel-intro">
    {:build_heading()}
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
                        {:token()}
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('Nickname')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input id="c-nickname" class="form-control" name="row[nickname]" type="text" readonly>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="0">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <label class="radio-inline">
                                    <input type="radio" name="row[status]" value="1" checked> {:__('Normal')}
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="row[status]" value="0"> {:__('Hidden')}
                                </label>
                            </div>
                        </div>

                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
require(['form'], function(Form) {
    Form.api.bindevent($("form[role=form]"));

    // 监听名称输入，自动转换为拼音昵称
    $("#c-name").on('input propertychange change', function() {
        var name = $(this).val();
        // 通过Ajax获取拼音
        $.post("{:url('ajax/get_pinyin')}", {text: name}, function(data) {
            $("#c-nickname").val(data);
        });
    });
});</script>