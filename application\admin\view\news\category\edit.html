{:build_heading()}
<div class="panel-body">
    <div id="myTabContent" class="tab-content">
        <div class="tab-pane fade active in" id="one">
            <div class="widget-body no-padding">
                <form id="edit-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
                    {:token()}
                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">{:__('Name')}:</label>
                        <div class="col-xs-12 col-sm-8">
                            <input id="c-name" data-rule="required" class="form-control" name="row[name]" type="text" value="{$row.name|htmlentities}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
                        <div class="col-xs-12 col-sm-8">
                            <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="{$row.weigh}">
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                        <div class="col-xs-12 col-sm-8">
                            {:build_radios('row[status]', ['normal'=>__('Normal'), 'hidden'=>__('Hidden')], $row['status'])}
                        </div>
                    </div>

                    <div class="form-group layer-footer">
                        <label class="control-label col-xs-12 col-sm-2"></label>
                        <div class="col-xs-12 col-sm-8">
                            <button type="submit" class="btn btn-success btn-embossed">{:__('OK')}</button>
                            <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
require(['form'], function(Form) {
    // 先添加disabled类，然后绑定事件（FastAdmin会自动移除disabled类）
    $(".layer-footer [type=submit]").addClass("disabled");
    Form.api.bindevent($("form[role=form]"));
});
</script>