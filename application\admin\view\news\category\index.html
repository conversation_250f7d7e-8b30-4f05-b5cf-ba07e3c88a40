{layout name="layout/default" /}

<div class="panel panel-default panel-intro">
    {:build_heading()}

    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del,import,export')}
                        <div class="dropdown btn-group">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">批量操作
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="javascript:;" class="btn-change" data-change="status" data-value="normal">设为正常</a></li>
                                <li><a href="javascript:;" class="btn-change" data-change="status" data-value="hidden">设为隐藏</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover table-nowrap"
                           data-operate-edit="{:$auth->check('news/category/edit')}"
                           data-operate-del="{:$auth->check('news/category/del')}"
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'template'], function ($, undefined, Backend, Table, Form, Template) {
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'news/category/index',
                    add_url: 'news/category/add',
                    edit_url: 'news/category/edit',
                    del_url: 'news/category/del',
                    multi_url: 'news/category/multi',
                    import_url: 'news/category/import',
                    table: 'news_category',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'weigh',
                searchFormVisible: true,
                showToggle: true,
                showColumns: true,
                showExport: true,
                showSearch: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'name', title: __('Name'), align: 'left', operate: 'LIKE'},
                        {field: 'weigh', title: __('Weigh'), sortable: true, operate: false},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate', 
                            title: __('Operate'), 
                            table: table, 
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: __('Detail'),
                                    icon: 'fa fa-list',
                                    classname: 'btn btn-info btn-xs btn-detail',
                                    url: 'news/category/detail'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 批量操作按钮点击事件
            $(document).on('click', '.btn-change', function () {
                var that = this;
                var ids = Table.api.selectedids(table);
                var data = {
                    status: $(that).data('value')
                };
                Layer.confirm('确认要批量更改选中的分类状态吗?', {icon: 3}, function (index) {
                    Table.api.multi("multi", ids, table, that);
                    Layer.close(index);
                });
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
</script> 