<div class="panel panel-default panel-intro">
    {:build_heading()}
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <form id="add-form" class="form-horizontal" role="form" data-toggle="validator" method="POST" action="">
                        {:token()}
                        <div class="form-group">
                            <label for="c-category_id" class="control-label col-xs-12 col-sm-2">{:__('Category')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select id="c-category_id" class="form-control selectpicker" name="row[category_id]" data-rule="required">
                                    {foreach name="categoryList" item="vo"}
                                        <option value="{$vo.id}">{$vo.name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c-tags" class="control-label col-xs-12 col-sm-2">{:__('标签')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <select id="c-tags" class="form-control selectpicker" multiple name="row[tags][]">
                                    {foreach name="tags" item="tag"}
                                        <option value="{$tag.id}">{$tag.name}</option>
                                    {/foreach}
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c-title" class="control-label col-xs-12 col-sm-2">{:__('Title')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input id="c-title" class="form-control" name="row[title]" type="text" data-rule="required">
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c-description" class="control-label col-xs-12 col-sm-2">{:__('描述')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-description" class="form-control" name="row[description]" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c-image" class="control-label col-xs-12 col-sm-2">{:__('图片')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <div class="input-group">
                                    <input id="c-image" class="form-control" size="50" name="row[image]" type="text">
                                    <div class="input-group-addon no-border no-padding">
                                        <span><button type="button" id="faupload-image" class="btn btn-danger faupload" data-input-id="c-image" data-mimetype="image/gif,image/jpeg,image/png,image/jpg,image/bmp" data-multiple="false" data-preview-id="p-image"><i class="fa fa-upload"></i> {:__('Upload')}</button></span>
                                        <span><button type="button" id="fachoose-image" class="btn btn-primary fachoose" data-input-id="c-image" data-mimetype="image/*" data-multiple="false"><i class="fa fa-list"></i> {:__('Choose')}</button></span>
                                    </div>
                                    <span class="msg-box n-right" for="c-image"></span>
                                </div>
                                <ul class="row list-inline faupload-preview" id="p-image"></ul>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c-content" class="control-label col-xs-12 col-sm-2">{:__('Content')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <textarea id="c-content" class="form-control editor" name="row[content]" rows="15" data-rule="required"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="c-weigh" class="control-label col-xs-12 col-sm-2">{:__('Weigh')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <input id="c-weigh" class="form-control" name="row[weigh]" type="number" value="0">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('置顶')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <label class="radio-inline">
                                    <input type="radio" name="row[is_top]" value="1"> {:__('Yes')}
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="row[is_top]" value="0" checked> {:__('No')}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('推荐')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <label class="radio-inline">
                                    <input type="radio" name="row[is_recommend]" value="1"> {:__('Yes')}
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="row[is_recommend]" value="0" checked> {:__('No')}
                                </label>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="control-label col-xs-12 col-sm-2">{:__('Status')}:</label>
                            <div class="col-xs-12 col-sm-8">
                                <label class="radio-inline">
                                    <input type="radio" name="row[status]" value="1" checked> {:__('Normal')}
                                </label>
                                <label class="radio-inline">
                                    <input type="radio" name="row[status]" value="0"> {:__('Hidden')}
                                </label>
                            </div>
                        </div>
                        <div class="form-group layer-footer">
                            <label class="control-label col-xs-12 col-sm-2"></label>
                            <div class="col-xs-12 col-sm-8">
                                <button type="submit" class="btn btn-primary btn-embossed disabled">{:__('OK')}</button>
                                <button type="reset" class="btn btn-default btn-embossed">{:__('Reset')}</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
require(['form', 'upload'], function (Form, Upload) {
    Form.api.bindevent($("form[role=form]"));
    Upload.api.plupload("#faupload-image"); //绑定图片上传按钮
    Upload.api.plupload("#fachoose-image"); //绑定图片选择按钮
});</script>