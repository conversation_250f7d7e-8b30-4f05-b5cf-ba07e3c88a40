{layout name="layout/default" /}

<div class="panel panel-default panel-intro">
    {:build_heading()}
    <div class="panel-body">
        <div id="myTabContent" class="tab-content">
            <div class="tab-pane fade active in" id="one">
                <div class="widget-body no-padding">
                    <div id="toolbar" class="toolbar">
                        {:build_toolbar('refresh,add,edit,del,import,export')}
                        <div class="dropdown btn-group">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-toggle="dropdown">批量操作
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu">
                                <li><a href="javascript:;" class="btn-change" data-change="status" data-value="normal">设为正常</a></li>
                                <li><a href="javascript:;" class="btn-change" data-change="status" data-value="hidden">设为隐藏</a></li>
                            </ul>
                        </div>
                    </div>
                    <table id="table" class="table table-striped table-bordered table-hover" 
                           data-operate-edit="{:$auth->check('news/news/edit')}" 
                           data-operate-del="{:$auth->check('news/news/del')}" 
                           width="100%">
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
define(['jquery', 'bootstrap', 'backend', 'table', 'form', 'template', 'upload'], function ($, undefined, Backend, Table, Form, Template, Upload) {
    var Controller = {
        index: function () {
            // 初始化表格参数配置
            Table.api.init({
                extend: {
                    index_url: 'news/news/index',
                    add_url: 'news/news/add',
                    edit_url: 'news/news/edit',
                    del_url: 'news/news/del',
                    multi_url: 'news/news/multi',
                    import_url: 'news/news/import',
                    table: 'news',
                }
            });

            var table = $("#table");

            // 初始化表格
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                searchFormVisible: true,
                showToggle: true,
                showColumns: true,
                showExport: true,
                showSearch: true,
                commonSearch: true,
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: __('Id'), sortable: true},
                        {field: 'category_id', title: __('Category'), visible: false},
                        {field: 'category.name', title: __('Category'), operate: 'LIKE'},
                        {field: 'title', title: __('Title'), align: 'left', operate: 'LIKE'},
                        {field: 'image', title: __('Image'), events: Table.api.events.image, formatter: Table.api.formatter.image},
                        {field: 'keywords', title: __('Keywords'), operate: 'LIKE'},
                        {field: 'description', title: __('Description'), operate: 'LIKE'},
                        {field: 'views', title: __('Views'), sortable: true},
                        {field: 'comments', title: __('Comments'), sortable: true},
                        {field: 'weigh', title: __('Weigh'), sortable: true},
                        {field: 'status', title: __('Status'), searchList: {"normal":__('Normal'),"hidden":__('Hidden')}, formatter: Table.api.formatter.status},
                        {field: 'publishtime', title: __('Publish Time'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: __('Createtime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {field: 'updatetime', title: __('Updatetime'), operate:'RANGE', addclass:'datetimerange', formatter: Table.api.formatter.datetime},
                        {
                            field: 'operate', 
                            title: __('Operate'), 
                            table: table, 
                            events: Table.api.events.operate,
                            buttons: [
                                {
                                    name: 'detail',
                                    text: __('Detail'),
                                    icon: 'fa fa-list',
                                    classname: 'btn btn-info btn-xs btn-detail',
                                    url: 'news/news/detail'
                                },
                                {
                                    name: 'comments',
                                    text: __('Comments'),
                                    icon: 'fa fa-comments',
                                    classname: 'btn btn-success btn-xs btn-dialog',
                                    url: 'news/comment/index?news_id={id}'
                                }
                            ],
                            formatter: Table.api.formatter.operate
                        }
                    ]
                ]
            });

            // 为表格绑定事件
            Table.api.bindevent(table);

            // 批量操作按钮点击事件
            $(document).on('click', '.btn-change', function () {
                var that = this;
                var ids = Table.api.selectedids(table);
                var data = {
                    status: $(that).data('value')
                };
                Layer.confirm('确认要批量更改选中的新闻状态吗?', {icon: 3}, function (index) {
                    Table.api.multi("multi", ids, table, that);
                    Layer.close(index);
                });
            });

            // 为表单绑定事件
            Form.api.bindevent($("form[role=form]"));

            // 绑定上传事件
            Upload.api.plupload($('#plupload-image'), function(data, ret){
                if (ret.code === 1) {
                    $('[name="row[image]"]').val(data.url);
                }
            });
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
});
</script>
