<?php

namespace app\index\controller;

use app\common\controller\Frontend;
use think\Db;

class Index extends Frontend
{

    protected $noNeedLogin = '*';
    protected $noNeedRight = '*';
    protected $layout = '';

    public function index()
    {
        // 获取新闻分类
        $categories = Db::name('news_category')
            ->where('status', 1)
            ->order('weigh DESC, id DESC')
            ->select();
        
        // 获取头条新闻（置顶新闻）
        $topNews = Db::name('news')
            ->where('status', 1)
            ->where('is_top', 1)
            ->order('id DESC')
            ->limit(1)
            ->select();

        // 获取普通新闻列表
        $newsList = Db::name('news')
            ->where('status', 1)
            ->order('id DESC')
            ->paginate(10);

        // 获取热门新闻
        $hotNews = Db::name('news')
            ->where('status', 1)
            ->order('views DESC')
            ->limit(10)
            ->select();

        // 获取推荐新闻
        $recommendNews = Db::name('news')
            ->where('status', 1)
            ->where('is_recommend', 1)
            ->order('id DESC')
            ->limit(5)
            ->select();

        // 示例社交平台数据
        // 抖音内容示例数据
        $douyinContent = [
            [
                'title' => '超火爆街头美食制作过程',
                'cover' => '__CDN__/assets/img/default.jpg',
                'likes' => '12.5w',
                'comments' => '2.3w',
                'shares' => '1.8w'
            ],
            [
                'title' => '年轻人上班摸鱼日常',
                'cover' => '__CDN__/assets/img/default.jpg',
                'likes' => '8.9w',
                'comments' => '1.5w',
                'shares' => '1.2w'
            ],
            [
                'title' => '超实用生活小妙招',
                'cover' => '__CDN__/assets/img/default.jpg',
                'likes' => '15.2w',
                'comments' => '3.1w',
                'shares' => '2.4w'
            ]
        ];

        // 快手内容示例数据
        $kuaishouContent = [
            [
                'title' => '农村生活日常记录',
                'cover' => '__CDN__/assets/img/default.jpg',
                'likes' => '20.1w',
                'comments' => '4.2w',
                'shares' => '3.5w'
            ],
            [
                'title' => '手工达人作品展示',
                'cover' => '__CDN__/assets/img/default.jpg',
                'likes' => '18.6w',
                'comments' => '3.8w',
                'shares' => '2.9w'
            ],
            [
                'title' => '搞笑短剧合集',
                'cover' => '__CDN__/assets/img/default.jpg',
                'likes' => '25.3w',
                'comments' => '5.1w',
                'shares' => '4.2w'
            ]
        ];

        // 知乎内容示例数据
        $zhihuContent = [
            [
                'question' => '为什么现在年轻人越来越注重工作与生活的平衡？',
                'answer' => '现代社会压力越来越大，年轻人开始意识到身心健康的重要性。同时，新一代年轻人的价值观也在发生变化，他们不再单纯追求物质条件，而是更注重生活品质和个人成长。此外，互联网的发展让年轻人接触到更多元的生活方式...',
                'upvotes' => '2.8k',
                'comments' => '526',
                'views' => '12.5w'
            ],
            [
                'question' => '如何看待互联网行业的996工作制？',
                'answer' => '996工作制是一个复杂的社会议题，需要从多个角度来分析。从企业角度来看，激烈的市场竞争确实需要高强度的工作投入。但从员工角度来看，长期超时工作会影响身心健康，降低工作效率...',
                'upvotes' => '3.2k',
                'comments' => '892',
                'views' => '15.8w'
            ],
            [
                'question' => '新手如何入门编程？',
                'answer' => '入门编程首先要选择一个适合自己的编程语言，建议从Python开始。Python语法简单，学习曲线平缓，社区资源丰富。可以从基础语法开始，然后逐步学习数据结构、算法等进阶知识...',
                'upvotes' => '1.9k',
                'comments' => '345',
                'views' => '8.9w'
            ]
        ];

        // 模板赋值
        $this->view->assign([
            'categories' => $categories,
            'topNews' => $topNews,
            'newsList' => $newsList,
            'hotNews' => $hotNews,
            'recommendNews' => $recommendNews,
            'douyinContent' => $douyinContent,
            'kuaishouContent' => $kuaishouContent,
            'zhihuContent' => $zhihuContent
        ]);

        return $this->view->fetch();
    }

}
