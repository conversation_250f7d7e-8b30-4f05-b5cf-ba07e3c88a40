<?php
namespace app\index\controller;

use think\Controller;
use think\Db;

class News extends Controller
{
    public function detail($id)
    {
        // 获取新闻详情
        $news = Db::name('news')
            ->alias('n')
            ->join('fa_news_category c', 'n.category_id = c.id')
            ->field('n.*, c.name as category_name')
            ->where('n.id', $id)
            ->where('n.status', 1)
            ->find();

        if (!$news) {
            $this->error('新闻不存在');
        }

        // 更新浏览次数
        Db::name('news')->where('id', $id)->setInc('views');

        // 获取相关新闻
        $relatedNews = Db::name('news')
            ->where('category_id', $news['category_id'])
            ->where('id', '<>', $id)
            ->where('status', 1)
            ->field('id, title, image, createtime, views')
            ->order('createtime desc')
            ->limit(5)
            ->select();

        // 获取热门新闻
        $hotNews = Db::name('news')
            ->where('status', 1)
            ->field('id, title, views')
            ->order('views desc')
            ->limit(10)
            ->select();

        // 获取所有分类
        $categories = Db::name('news_category')
            ->where('status', 1)
            ->order('weigh desc, id desc')
            ->select();

        // 获取网站配置
        $site = Db::name('config')
            ->where('group', 'site')
            ->column('value', 'name');

        $this->assign([
            'site' => $site,
            'news' => $news,
            'relatedNews' => $relatedNews,
            'hotNews' => $hotNews,
            'categories' => $categories
        ]);

        return $this->fetch();
    }
} 