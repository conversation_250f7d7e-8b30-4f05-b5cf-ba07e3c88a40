<!DOCTYPE html>
<html>

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>{$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico"/>
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <style>
        /* 社交平台内容区样式 */
        .social-media-section {
            margin: 40px 0;
            padding: 20px 0;
            background: #f8f9fa;
        }

        .section-title {
            margin-bottom: 30px;
            text-align: center;
            color: #333;
            font-weight: 600;
        }

        .nav-tabs {
            border-bottom: 2px solid #dee2e6;
            margin-bottom: 20px;
        }

        .nav-tabs > li > a {
            color: #495057;
            font-size: 16px;
            padding: 10px 20px;
        }

        .nav-tabs > li.active > a,
        .nav-tabs > li.active > a:focus,
        .nav-tabs > li.active > a:hover {
            color: #007bff;
            border: none;
            border-bottom: 2px solid #007bff;
            background: transparent;
        }

        .platform-item {
            background: #fff;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: transform 0.2s;
        }

        .platform-item:hover {
            transform: translateY(-5px);
        }

        .media-thumbnail {
            position: relative;
            padding-top: 56.25%; /* 16:9 比例 */
            overflow: hidden;
        }

        .media-thumbnail img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .play-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #fff;
            font-size: 48px;
            opacity: 0.8;
            transition: opacity 0.2s;
        }

        .platform-item:hover .play-overlay {
            opacity: 1;
        }

        .media-info {
            padding: 15px;
        }

        .media-info h4 {
            margin: 0 0 10px;
            font-size: 16px;
            line-height: 1.4;
            height: 44px;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
        }

        .stats {
            color: #6c757d;
            font-size: 14px;
        }

        .stats span {
            margin-right: 15px;
        }

        .stats i {
            margin-right: 5px;
        }

        /* 知乎特殊样式 */
        .zhihu-item {
            padding: 15px;
        }

        .question-title h4 {
            color: #1a1a1a;
            margin-bottom: 10px;
        }

        .answer-preview {
            color: #646464;
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 15px;
            height: 70px;
            overflow: hidden;
        }

        .recommended-news .news-sidebar-list {
            padding: 0;
            list-style: none;
        }
        
        .recommended-news .recommended-item {
            display: flex;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .recommended-news .news-sidebar-image {
            width: 100px;
            height: 70px;
            margin-right: 10px;
            overflow: hidden;
            flex-shrink: 0;
        }
        
        .recommended-news .news-sidebar-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .recommended-news .news-sidebar-content {
            flex: 1;
            overflow: hidden;
        }
        
        .recommended-news .news-title {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 5px;
            color: #333;
        }
        
        .recommended-news .news-meta {
            font-size: 12px;
            color: #999;
        }
        
        .recommended-news .news-meta span {
            margin-right: 10px;
        }
        
        .recommended-news .news-meta i {
            margin-right: 3px;
        }
    </style>
</head>

<body>
    <!-- 顶部导航 -->
    <nav class="navbar navbar-default">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="/">{$site.name|htmlentities}</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
                <ul class="nav navbar-nav">
                    <li class="active"><a href="/">首页</a></li>
                    {foreach name="categories" item="category"}
                    <li><a href="{:url('index/category/index', ['id'=>$category.id])}">{$category.name|htmlentities}</a></li>
                    {/foreach}
                </ul>
                <ul class="nav navbar-nav navbar-right">
                    <li><a href="{:url('index/user/index', '', false, true)}">{:__('Member center')}</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主体内容 -->
    <div class="container">
        <!-- 轮播图和热点新闻组合区域 -->
        <div class="row featured-section">
            <!-- 热点新闻 -->
            <div class="col-md-4">
                <div class="hot-news-sidebar">
                    <h3 class="hot-news-title">热点新闻</h3>
                    <ul class="hot-news-list">
                        {volist name="hotNews" id="news" offset="0" length="4"}
                        <li>
                            <a href="{:url('index/news/detail',['id'=>$news.id])}">
                                <span class="hot-news-label">热</span>
                                {$news.title|htmlentities}
                            </a>
                        </li>
                        {/volist}
                    </ul>
                </div>
            </div>

            <!-- 轮播图 -->
            <div class="col-md-8">
                <div id="newsCarousel" class="carousel slide" data-ride="carousel">
                    <!-- 轮播指示器 -->
                    <ol class="carousel-indicators">
                        {volist name="topNews" id="news" offset="0" length="3"}
                        <li data-target="#newsCarousel" data-slide-to="{$i-1}" class="{if $i == 1}active{/if}"></li>
                        {/volist}
                    </ol>

                    <!-- 轮播项目 -->
                    <div class="carousel-inner">
                        {volist name="topNews" id="news" offset="0" length="3"}
                        <div class="item {if $i == 1}active{/if}">
                            <a href="{:url('index/news/detail',['id'=>$news.id])}">
                                <img src="{$news.image|default='__CDN__/assets/img/default.jpg'}" alt="{$news.title|htmlentities}">
                                <div class="carousel-caption">
                                    <h3>{$news.title|htmlentities}</h3>
                                    <p class="news-desc">{$news.description|htmlentities|mb_substr=0,50}...</p>
                                </div>
                            </a>
                        </div>
                        {/volist}
                    </div>

                    <!-- 轮播控制 -->
                    <a class="left carousel-control" href="#newsCarousel" data-slide="prev">
                        <span class="glyphicon glyphicon-chevron-left"></span>
                    </a>
                    <a class="right carousel-control" href="#newsCarousel" data-slide="next">
                        <span class="glyphicon glyphicon-chevron-right"></span>
                    </a>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="row">
            <!-- 主要新闻列表 -->
            <div class="col-md-8">
                <!-- 头条新闻 -->
                <div class="top-news">
                    {volist name="topNews" id="news" offset="0" length="1"}
                    <div class="featured-news">
                        <h2><a href="{:url('index/news/detail',['id'=>$news.id])}">{$news.title|htmlentities}</a></h2>
                        <div class="news-meta">
                            <span><i class="fa fa-clock-o"></i> {:date("Y-m-d H:i", $news.createtime)}</span>
                            <span><i class="fa fa-eye"></i> {$news.views}</span>
                        </div>
                        {if $news.image}
                        <div class="news-image">
                            <img src="{$news.image}" alt="{$news.title|htmlentities}" class="img-responsive">
                        </div>
                        {/if}
                        <p class="news-description">{$news.description|htmlentities}</p>
                    </div>
                    {/volist}
                </div>

                <!-- 新闻列表 -->
                <div class="news-list">
                    {volist name="newsList" id="news"}
                    <div class="news-item">
                        <div class="row">
                            {if $news.image}
                            <div class="col-md-3">
                                <div class="news-image">
                                    <img src="{$news.image}" alt="{$news.title|htmlentities}" class="img-responsive">
                                </div>
                            </div>
                            <div class="col-md-9">
                            {else}
                            <div class="col-md-12">
                            {/if}
                                <h3><a href="{:url('index/news/detail',['id'=>$news.id])}">{$news.title|htmlentities}</a></h3>
                                <div class="news-meta">
                                    <span><i class="fa fa-clock-o"></i> {:date("Y-m-d H:i", $news.createtime)}</span>
                                    <span><i class="fa fa-eye"></i> {$news.views}</span>
                                    <span><i class="fa fa-folder"></i> {$news.category_name|htmlentities}</span>
                                </div>
                                <p class="news-description">{$news.description|htmlentities}</p>
                            </div>
                        </div>
                    </div>
                    {/volist}
                </div>

                <!-- 分页 -->
                <div class="pagination-wrapper">
                    {$newsList}
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="col-md-4">
                <!-- 热门新闻 -->
                <div class="sidebar-box hot-news">
                    <h3 class="sidebar-title">热门新闻</h3>
                    <ul class="news-sidebar-list">
                        {volist name="hotNews" id="news"}
                        <li>
                            <a href="{:url('index/news/detail',['id'=>$news.id])}">
                                <span class="news-title">{$news.title|htmlentities}</span>
                                <span class="news-views"><i class="fa fa-eye"></i> {$news.views}</span>
                            </a>
                        </li>
                        {/volist}
                    </ul>
                </div>

                <!-- 推荐新闻 -->
                <div class="sidebar-box recommended-news">
                    <h3 class="sidebar-title">推荐阅读</h3>
                    <ul class="news-sidebar-list">
                        {volist name="recommendNews" id="news"}
                        <li class="recommended-item">
                            {if $news.image}
                            <div class="news-sidebar-image">
                                <img src="{$news.image}" alt="{$news.title|htmlentities}">
                            </div>
                            {/if}
                            <div class="news-sidebar-content">
                                <a href="{:url('index/news/detail',['id'=>$news.id])}">
                                    <span class="news-title">{$news.title|htmlentities}</span>
                                </a>
                                <div class="news-meta">
                                    <span><i class="fa fa-clock-o"></i> {:date("m-d", $news.createtime)}</span>
                                    <span><i class="fa fa-eye"></i> {$news.views}</span>
                                </div>
                            </div>
                        </li>
                        {/volist}
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 社交平台内容展示区 -->
    <div class="container social-media-section">
        <div class="row">
            <div class="col-md-12">
                <h2 class="section-title">热门平台内容</h2>
                <!-- 平台选项卡 -->
                <ul class="nav nav-tabs" role="tablist">
                    <li role="presentation" class="active">
                        <a href="#douyin" aria-controls="douyin" role="tab" data-toggle="tab">
                            <i class="fa fa-video-camera"></i> 抖音精选
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#kuaishou" aria-controls="kuaishou" role="tab" data-toggle="tab">
                            <i class="fa fa-play-circle"></i> 快手热门
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#zhihu" aria-controls="zhihu" role="tab" data-toggle="tab">
                            <i class="fa fa-question-circle"></i> 知乎精华
                        </a>
                    </li>
                </ul>

                <!-- 平台内容区 -->
                <div class="tab-content">
                    <!-- 抖音内容 -->
                    <div role="tabpanel" class="tab-pane active" id="douyin">
                        <div class="row platform-content">
                            {volist name="douyinContent" id="item" empty="$empty"}
                            <div class="col-md-4">
                                <div class="platform-item">
                                    <div class="media-thumbnail">
                                        <img src="{$item.cover|default='__CDN__/assets/img/video-placeholder.jpg'}" class="img-responsive">
                                        <div class="play-overlay">
                                            <i class="fa fa-play-circle"></i>
                                        </div>
                                    </div>
                                    <div class="media-info">
                                        <h4>{$item.title|htmlentities}</h4>
                                        <div class="stats">
                                            <span><i class="fa fa-heart"></i> {$item.likes}</span>
                                            <span><i class="fa fa-comment"></i> {$item.comments}</span>
                                            <span><i class="fa fa-share"></i> {$item.shares}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>

                    <!-- 快手内容 -->
                    <div role="tabpanel" class="tab-pane" id="kuaishou">
                        <div class="row platform-content">
                            {volist name="kuaishouContent" id="item" empty="$empty"}
                            <div class="col-md-4">
                                <div class="platform-item">
                                    <div class="media-thumbnail">
                                        <img src="{$item.cover|default='__CDN__/assets/img/video-placeholder.jpg'}" class="img-responsive">
                                        <div class="play-overlay">
                                            <i class="fa fa-play-circle"></i>
                                        </div>
                                    </div>
                                    <div class="media-info">
                                        <h4>{$item.title|htmlentities}</h4>
                                        <div class="stats">
                                            <span><i class="fa fa-heart"></i> {$item.likes}</span>
                                            <span><i class="fa fa-comment"></i> {$item.comments}</span>
                                            <span><i class="fa fa-share"></i> {$item.shares}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>

                    <!-- 知乎内容 -->
                    <div role="tabpanel" class="tab-pane" id="zhihu">
                        <div class="row platform-content">
                            {volist name="zhihuContent" id="item" empty="$empty"}
                            <div class="col-md-4">
                                <div class="platform-item zhihu-item">
                                    <div class="question-title">
                                        <h4>{$item.question|htmlentities}</h4>
                                    </div>
                                    <div class="answer-preview">
                                        <p>{$item.answer|htmlentities|mb_substr=0,100}...</p>
                                    </div>
                                    <div class="stats">
                                        <span><i class="fa fa-thumbs-up"></i> {$item.upvotes}</span>
                                        <span><i class="fa fa-comment"></i> {$item.comments}</span>
                                        <span><i class="fa fa-eye"></i> {$item.views}</span>
                                    </div>
                                </div>
                            </div>
                            {/volist}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>Copyright @ {$site.name|htmlentities} {:date('Y', time())} 版权所有</p>
                </div>
                <div class="col-md-6 text-right">
                    <p><a href="https://beian.miit.gov.cn" target="_blank">{$site.beian|htmlentities}</a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JS文件 -->
    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
    <script>
    $(document).ready(function(){
        // 初始化轮播图
        $('#newsCarousel').carousel({
            interval: 5000,  // 5秒切换一次
            pause: 'hover', // 鼠标悬停时暂停
            wrap: true      // 循环播放
        });

        // 鼠标悬停时显示控制按钮
        $('#newsCarousel').hover(
            function() {
                $(this).find('.carousel-control').css('opacity', '0.8');
            },
            function() {
                $(this).find('.carousel-control').css('opacity', '0');
            }
        );

        // 触摸滑动支持
        var touchStartX = 0;
        var touchEndX = 0;
        
        $('#newsCarousel').on('touchstart', function(event) {
            touchStartX = event.originalEvent.touches[0].pageX;
        });

        $('#newsCarousel').on('touchend', function(event) {
            touchEndX = event.originalEvent.changedTouches[0].pageX;
            handleSwipe();
        });

        function handleSwipe() {
            if (touchStartX - touchEndX > 50) {
                $('#newsCarousel').carousel('next');
            }
            if (touchEndX - touchStartX > 50) {
                $('#newsCarousel').carousel('prev');
            }
        }
    });
    </script>
</body>

</html>
