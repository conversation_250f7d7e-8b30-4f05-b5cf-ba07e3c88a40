<!DOCTYPE html>
<html>

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>{$news.title|htmlentities} - {$site.name|htmlentities}</title>
    <link rel="shortcut icon" href="__CDN__/assets/img/favicon.ico" />
    <link href="__CDN__/assets/css/bootstrap.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/font-awesome.min.css" rel="stylesheet">
    <link href="__CDN__/assets/css/index.css" rel="stylesheet">
    <style>
        .news-detail {
            background: #fff;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        
        .news-detail .news-title {
            font-size: 28px;
            font-weight: 600;
            color: #2d3436;
            margin: 0 0 20px;
            line-height: 1.4;
        }
        
        .news-detail .news-meta {
            color: #636e72;
            font-size: 14px;
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .news-detail .news-meta span {
            margin-right: 20px;
        }
        
        .news-detail .news-meta i {
            margin-right: 5px;
            color: #6c5ce7;
        }

        .news-detail .news-image {
            margin: 20px 0;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .news-detail .news-image img {
            width: 100%;
            height: auto;
            display: block;
        }
        
        .news-detail .news-content {
            font-size: 16px;
            line-height: 1.8;
            color: #2d3436;
        }
        
        .news-detail .news-content img {
            max-width: 100%;
            height: auto;
            margin: 15px 0;
            border-radius: 8px;
        }
        
        .news-detail .news-content p {
            margin-bottom: 20px;
        }

        .news-detail .news-description {
            font-size: 18px;
            line-height: 1.6;
            color: #636e72;
            margin: 20px 0;
            padding: 15px;
            background: #f5f6fa;
            border-left: 4px solid #6c5ce7;
            border-radius: 4px;
        }
        
        .sidebar-box {
            background: #fff;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            margin-bottom: 30px;
        }
        
        .sidebar-title {
            color: #2d3436;
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #6c5ce7;
            position: relative;
        }
        
        .sidebar-title:after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: #a363d9;
        }
        
        .related-news-list,
        .hot-news-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        
        .related-news-list li,
        .hot-news-list li {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
            transition: all 0.3s ease;
        }
        
        .related-news-list li:last-child,
        .hot-news-list li:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }
        
        .related-news-list li:hover,
        .hot-news-list li:hover {
            transform: translateX(5px);
        }
        
        .related-news-list a,
        .hot-news-list a {
            color: #2d3436;
            text-decoration: none;
            font-size: 15px;
            line-height: 1.5;
            display: block;
        }
        
        .related-news-list a:hover,
        .hot-news-list a:hover {
            color: #6c5ce7;
        }
        
        .hot-news-label {
            display: inline-block;
            padding: 2px 6px;
            background: linear-gradient(135deg, #e74c3c, #ff7675);
            color: #fff;
            border-radius: 4px;
            font-size: 12px;
            margin-right: 5px;
        }

        .related-news-list .news-item-meta {
            font-size: 12px;
            color: #636e72;
            margin-top: 5px;
        }

        .related-news-list .news-item-meta span {
            margin-right: 15px;
        }

        .related-news-list .news-item-meta i {
            margin-right: 3px;
            color: #6c5ce7;
        }
    </style>
</head>

<body>
    <!-- 顶部导航 -->
    <nav class="navbar navbar-default">
        <div class="container">
            <div class="navbar-header">
                <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar"
                    aria-expanded="false">
                    <span class="sr-only">Toggle navigation</span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                    <span class="icon-bar"></span>
                </button>
                <a class="navbar-brand" href="/">{$site.name|htmlentities}</a>
            </div>
            <div class="collapse navbar-collapse" id="navbar">
                <ul class="nav navbar-nav">
                    <li><a href="/">首页</a></li>
                    {foreach name="categories" item="category"}
                    <li class="{if $category.id == $news.category_id}active{/if}">
                        <a href="{:url('index/category/index', ['id'=>$category.id])}">{$category.name|htmlentities}</a>
                    </li>
                    {/foreach}
                </ul>
                <ul class="nav navbar-nav navbar-right">
                    <li><a href="{:url('index/user/index', '', false, true)}">{:__('Member center')}</a></li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主体内容 -->
    <div class="container">
        <div class="row">
            <!-- 新闻详情 -->
            <div class="col-md-8">
                <div class="news-detail">
                    <h1 class="news-title">{$news.title|htmlentities}</h1>
                    <div class="news-meta">
                        <span><i class="fa fa-clock-o"></i> {:date("Y-m-d H:i", $news.createtime)}</span>
                        <span><i class="fa fa-eye"></i> {$news.views} 次浏览</span>
                        <span><i class="fa fa-folder"></i> {$news.category_name|htmlentities}</span>
                        {if isset($news['author']) && $news['author']}
                        <span><i class="fa fa-user"></i> {$news.author|htmlentities}</span>
                        {/if}
                    </div>

                    {if $news.description}
                    <div class="news-description">
                        {$news.description|htmlentities}
                    </div>
                    {/if}

                    {if $news.image}
                    <div class="news-image">
                        <img src="{$news.image}" alt="{$news.title|htmlentities}">
                    </div>
                    {/if}

                    <div class="news-content">
                        {$news.content}
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="col-md-4">
                <!-- 相关新闻 -->
                <div class="sidebar-box">
                    <h3 class="sidebar-title">相关新闻</h3>
                    <ul class="related-news-list">
                        {volist name="relatedNews" id="related"}
                        <li>
                            <a href="{:url('index/news/detail',['id'=>$related.id])}">
                                {$related.title|htmlentities}
                                <div class="news-item-meta">
                                    <span><i class="fa fa-clock-o"></i> {:date("Y-m-d", $related.createtime)}</span>
                                    <span><i class="fa fa-eye"></i> {$related.views}</span>
                                </div>
                            </a>
                        </li>
                        {/volist}
                    </ul>
                </div>

                <!-- 热门新闻 -->
                <div class="sidebar-box">
                    <h3 class="sidebar-title">热门新闻</h3>
                    <ul class="hot-news-list">
                        {volist name="hotNews" id="hot"}
                        <li>
                            <a href="{:url('index/news/detail',['id'=>$hot.id])}">
                                <span class="hot-news-label">热</span>
                                {$hot.title|htmlentities}
                            </a>
                        </li>
                        {/volist}
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 页脚 -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-md-6">
                    <p>Copyright @ {$site.name|htmlentities} {:date('Y', time())} 版权所有</p>
                </div>
                <div class="col-md-6 text-right">
                    <p><a href="https://beian.miit.gov.cn" target="_blank">{$site.beian|htmlentities}</a></p>
                </div>
            </div>
        </div>
    </footer>

    <!-- JS文件 -->
    <script src="__CDN__/assets/js/jquery.min.js"></script>
    <script src="__CDN__/assets/js/bootstrap.min.js"></script>
</body>

</html> 