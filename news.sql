-- 新闻分类表
CREATE TABLE IF NOT EXISTS `fa_news_category` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `pid` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '父ID',
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
    `nickname` varchar(50) NOT NULL DEFAULT '' COMMENT '别名',
    `flag` set('hot','index','recommend') NOT NULL DEFAULT '' COMMENT '标志',
    `image` varchar(100) NOT NULL DEFAULT '' COMMENT '图片',
    `keywords` varchar(255) NOT NULL DEFAULT '' COMMENT '关键字',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
    `diyname` varchar(30) NOT NULL DEFAULT '' COMMENT '自定义名称',
    `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
    PRIMARY KEY (`id`),
    KEY `weigh` (`weigh`,`id`),
    KEY `pid` (`pid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻分类表';

-- 新闻内容表
CREATE TABLE IF NOT EXISTS `fa_news` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `category_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '分类ID',
    `title` varchar(100) NOT NULL DEFAULT '' COMMENT '标题',
    `description` varchar(255) NOT NULL DEFAULT '' COMMENT '描述',
    `content` text COMMENT '内容',
    `image` varchar(100) NOT NULL DEFAULT '' COMMENT '图片',
    `views` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '点击次数',
    `comments` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '评论次数',
    `weigh` int(10) NOT NULL DEFAULT '0' COMMENT '权重',
    `is_top` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否置顶',
    `is_recommend` tinyint(1) unsigned NOT NULL DEFAULT '0' COMMENT '是否推荐',
    `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
    `keywords` varchar(255) NOT NULL DEFAULT '' COMMENT '关键词',
    `tags` varchar(255) NOT NULL DEFAULT '' COMMENT '标签',
    PRIMARY KEY (`id`),
    KEY `category_id` (`category_id`),
    KEY `weigh` (`weigh`,`id`),
    FOREIGN KEY (`category_id`) REFERENCES `fa_news_category` (`id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻表';

-- 插入新闻分类数据
INSERT INTO `fa_news_category` (`id`, `pid`, `name`, `nickname`, `flag`, `image`, `keywords`, `description`, `createtime`, `updatetime`, `weigh`, `status`) VALUES
(1, 0, '热点新闻', 'hot-news', 'hot,recommend', '/assets/img/hot-news.jpg', '热点,要闻', '最新热点新闻动态', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 100, 1),
(2, 0, '国际新闻', 'international', 'index', '/assets/img/international.jpg', '国际,世界,环球', '全球重要新闻资讯', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 90, 1),
(3, 0, '科技资讯', 'technology', 'recommend', '/assets/img/tech.jpg', '科技,互联网,创新', '最新科技发展动态', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 80, 1),
(4, 0, '财经资讯', 'finance', 'index', '/assets/img/finance.jpg', '财经,金融,经济', '财经市场分析与动态', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 70, 1),
(5, 0, '体育新闻', 'sports', 'hot', '/assets/img/sports.jpg', '体育,赛事,运动', '体育赛事新闻报道', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 60, 1),
(6, 0, '文化教育', 'culture', '', '/assets/img/culture.jpg', '文化,教育,艺术', '文化教育相关新闻', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 50, 1);

-- 新闻标签表
CREATE TABLE IF NOT EXISTS `fa_news_tags` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `name` varchar(50) NOT NULL DEFAULT '' COMMENT '标签名称',
    `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    `updatetime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '更新时间',
    `status` tinyint(1) unsigned NOT NULL DEFAULT '1' COMMENT '状态',
    PRIMARY KEY (`id`),
    UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻标签表';

-- 新闻-标签关联表
CREATE TABLE IF NOT EXISTS `fa_news_tags_relation` (
    `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `news_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '新闻ID',
    `tag_id` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '标签ID',
    `createtime` int(10) unsigned NOT NULL DEFAULT '0' COMMENT '创建时间',
    PRIMARY KEY (`id`),
    KEY `news_id` (`news_id`),
    KEY `tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='新闻-标签关联表';

-- 插入新闻标签数据
INSERT INTO `fa_news_tags` (`id`, `name`, `createtime`, `updatetime`, `status`) VALUES
(1, '政治', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(2, '经济', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(3, '科技创新', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(4, '人工智能', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(5, '互联网', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(6, '股市', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(7, '数字货币', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(8, '足球', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(9, '篮球', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(10, '奥运', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(11, '教育改革', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(12, '艺术展览', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(13, '文化遗产', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(14, '全球化', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(15, '环境保护', UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1);

-- 插入示例新闻数据
INSERT INTO `fa_news` (`id`, `category_id`, `title`, `description`, `keywords`, `tags`, `content`, `image`, `views`, `comments`, `weigh`, `is_top`, `is_recommend`, `createtime`, `updatetime`, `status`) VALUES
(1, 1, '全球经济发展新趋势', '探讨后疫情时代全球经济发展新方向', '经济,全球化,发展', '经济,全球化', '新闻内容详情', '/assets/img/news/economy1.jpg', 1000, 50, 100, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(2, 3, 'AI技术革新与应用', '最新人工智能技术发展与行业应用分析', '人工智能,科技,创新', '科技创新,人工智能', '新闻内容详情', '/assets/img/news/ai1.jpg', 800, 30, 90, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(3, 4, '数字货币市场分析', '全球数字货币市场最新动态', '数字货币,金融,区块链', '数字货币,经济', '新闻内容详情', '/assets/img/news/crypto1.jpg', 600, 20, 80, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(4, 5, '世界杯最新战报', '足球世界杯赛事进展与分析', '足球,世界杯,体育', '足球,奥运', '新闻内容详情', '/assets/img/news/worldcup1.jpg', 1500, 100, 95, 1, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1),
(5, 6, '全球艺术展览盛会', '国际艺术展览最新动态报道', '艺术,文化,展览', '艺术展览,文化遗产', '新闻内容详情', '/assets/img/news/art1.jpg', 400, 15, 70, 0, 1, UNIX_TIMESTAMP(), UNIX_TIMESTAMP(), 1);

-- 插入新闻-标签关联数据
INSERT INTO `fa_news_tags_relation` (`news_id`, `tag_id`, `createtime`) VALUES
-- 新闻1的标签关联
(1, 2, UNIX_TIMESTAMP()),  -- 经济
(1, 14, UNIX_TIMESTAMP()), -- 全球化
(1, 15, UNIX_TIMESTAMP()), -- 环境保护

-- 新闻2的标签关联
(2, 3, UNIX_TIMESTAMP()),  -- 科技创新
(2, 4, UNIX_TIMESTAMP()),  -- 人工智能
(2, 5, UNIX_TIMESTAMP()),  -- 互联网

-- 新闻3的标签关联
(3, 2, UNIX_TIMESTAMP()),  -- 经济
(3, 7, UNIX_TIMESTAMP()),  -- 数字货币
(3, 5, UNIX_TIMESTAMP()),  -- 互联网

-- 新闻4的标签关联
(4, 8, UNIX_TIMESTAMP()),  -- 足球
(4, 10, UNIX_TIMESTAMP()), -- 奥运

-- 新闻5的标签关联
(5, 12, UNIX_TIMESTAMP()), -- 艺术展览
(5, 13, UNIX_TIMESTAMP()); -- 文化遗产