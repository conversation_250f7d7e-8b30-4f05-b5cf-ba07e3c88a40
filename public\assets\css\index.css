html,
body {
    height: 100%;
    width: 100%;
}

* {
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
}

body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-weight: 400;
    margin: 0;
    background-color: #f5f6fa;
    line-height: 1.6;
}

a {
    -webkit-transition: all 0.35s;
    -moz-transition: all 0.35s;
    transition: all 0.35s;
    color: #474157;
}

a:hover,
a:focus {
    color: #474157;
}

hr {
    max-width: 100px;
    margin: 25px auto 0;
    border-width: 1px;
    border-color: rgba(34, 34, 34, 0.1);
}

hr.light {
    border-color: white;
}

h1,
h2,
h3,
h4,
h5,
h6 {
    font-weight: 200;
    letter-spacing: 1px;
}

p {
    font-size: 16px;
    line-height: 1.5;
    margin-bottom: 20px;
}


#mainbody {
    position: relative;
    width: 100%;
    min-height: auto;
    overflow-y: hidden;
    background: #f1f6fd;
    color: #474157;
    height: calc(100vh - 61px);
}

#mainbody > .container > .text-center {
    text-align: center;
    padding: 350px 0 50px;
    position: relative;
    height: 100vh;
}

#mainbody > .container > .text-center h1 {
    font-size: 50px;
    font-weight: bold;
    color: #171347
}

#mainbody > .container > .text-center > a {
    background: #fdcc52;
    background: -webkit-linear-gradient(#fdcc52, #fdc539);
    background: linear-gradient(#fdcc52, #fdc539);
    text-transform: uppercase;
    letter-spacing: 2px;
    border-radius: 300px;
    margin-top: 20px;
    padding: 10px 45px;
    font-size: 14px;
    text-decoration: none;
}

@media (max-height: 500px) {
    #mainbody {
        height: inherit;
    }
}

@media (min-width: 768px) {
    .navbar-default {
        background-color: transparent;
        border-color: transparent;
    }

    #mainbody .index-text {
        text-align: left;
    }
}

@media (max-width: 767px) {

    #mainbody > .container > .text-center {


        padding: 130px 0 0 0;
        height: calc(100vh - 261px);
    }

    #mainbody > .container > .text-center > h1 {


        font-size: 50px;
        margin-bottom: 20px;
    }
}

.footer {
    background: linear-gradient(135deg, #2d3436, #636e72);
    color: #fff;
    padding: 30px 0;
    margin-top: 50px;
}

.footer p {
    margin: 0;
}

.footer a {
    color: #fff;
    text-decoration: none;
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.footer a:hover {
    opacity: 1;
}

/* 轮播图和热点新闻组合区域 */
.featured-section {
    margin: 25px 0;
    background: transparent;
}

/* 轮播图样式 */
#newsCarousel {
    border-radius: 12px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    overflow: hidden;
}

#newsCarousel .carousel-inner {
    height: 400px;
}

#newsCarousel .item {
    height: 400px;
    position: relative;
    transition: transform .6s ease-in-out;
}

#newsCarousel .item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform .3s ease;
}

#newsCarousel .item:hover img {
    transform: scale(1.02);
}

#newsCarousel .carousel-caption {
    background: linear-gradient(to top, rgba(0,0,0,0.8) 0%, rgba(0,0,0,0.4) 50%, transparent 100%);
    padding: 40px 20px 30px;
}

#newsCarousel .carousel-caption h3 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

#newsCarousel .news-desc {
    font-size: 15px;
    line-height: 1.6;
    opacity: 0.9;
    max-width: 80%;
}

#newsCarousel .carousel-control {
    background: none;
    opacity: 0;
    transition: all .3s ease;
    width: 10%;
}

#newsCarousel:hover .carousel-control {
    opacity: 0.8;
}

#newsCarousel .carousel-control span {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    font-size: 30px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: rgba(0,0,0,0.5);
    border-radius: 50%;
}

#newsCarousel .carousel-control.left span {
    left: 15px;
}

#newsCarousel .carousel-control.right span {
    right: 15px;
}

#newsCarousel .carousel-indicators {
    bottom: 15px;
}

#newsCarousel .carousel-indicators li {
    width: 12px;
    height: 12px;
    margin: 0 4px;
    background-color: rgba(255,255,255,0.5);
    border: none;
    border-radius: 50%;
    transition: all .3s ease;
}

#newsCarousel .carousel-indicators li.active {
    width: 12px;
    height: 12px;
    background-color: #fff;
    margin: 0 4px;
}

/* 热点新闻侧边栏样式 */
.hot-news-sidebar {
    background: #fff;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    height: 400px;
    border: none;
}

.hot-news-title {
    color: #2d3436;
    font-size: 22px;
    font-weight: 600;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #6c5ce7;
    position: relative;
}

.hot-news-title:after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 50px;
    height: 2px;
    background: #a363d9;
}

.hot-news-list {
    margin: 0;
    padding: 0;
}

.hot-news-list li {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
    transition: all 0.3s ease;
}

.hot-news-list li:hover {
    transform: translateX(5px);
}

.hot-news-list a {
    display: flex;
    align-items: center;
    color: #2d3436;
    font-size: 15px;
    line-height: 1.5;
    text-decoration: none;
}

.hot-news-label {
    background: linear-gradient(135deg, #e74c3c, #ff7675);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    margin-right: 10px;
    flex-shrink: 0;
}

/* 响应式调整 */
@media (max-width: 991px) {
    .hot-news-sidebar {
        height: auto;
        margin-bottom: 20px;
    }
    
    .news-item .news-image img {
        height: 180px;
    }
}

@media (max-width: 767px) {
    .navbar-default .navbar-toggle {
        border-color: rgba(255,255,255,0.5);
    }
    
    .navbar-default .navbar-toggle .icon-bar {
        background-color: #fff;
    }
    
    .news-item .news-image img {
        height: 160px;
    }
    
    .hot-news-title {
        font-size: 20px;
    }
}

/* 主体内容区域 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* 确保内容居中 */
.row {
    margin-left: -15px;
    margin-right: -15px;
}

/* 导航栏样式 */
.navbar-default {
    background: linear-gradient(135deg, #6c5ce7, #a363d9);
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.navbar-default .navbar-brand,
.navbar-default .navbar-nav > li > a {
    color: #fff;
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-default .navbar-brand:hover,
.navbar-default .navbar-nav > li > a:hover {
    color: #fff;
    opacity: 0.8;
}

.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover {
    background: rgba(255,255,255,0.1);
    color: #fff;
}

/* 主要新闻列表样式 */
.news-list {
    margin-top: 30px;
}

.news-item {
    background: #fff;
    border-radius: 12px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.news-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.1);
}

.news-item .news-image {
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
}

.news-item .news-image img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-item:hover .news-image img {
    transform: scale(1.05);
}

.news-item h3 {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 10px;
    line-height: 1.4;
}

.news-item h3 a {
    color: #2d3436;
    text-decoration: none;
    transition: color 0.3s ease;
}

.news-item h3 a:hover {
    color: #6c5ce7;
}

.news-meta {
    color: #636e72;
    font-size: 14px;
    margin-bottom: 12px;
}

.news-meta span {
    margin-right: 15px;
}

.news-meta i {
    margin-right: 5px;
    color: #6c5ce7;
}

.news-description {
    color: #636e72;
    line-height: 1.6;
    margin: 0;
}

/* 分页样式 */
.pagination-wrapper {
    margin: 30px 0;
    text-align: center;
}

.pagination > li > a,
.pagination > li > span {
    color: #6c5ce7;
    border: 1px solid #ddd;
    margin: 0 3px;
    border-radius: 4px;
}

.pagination > .active > a,
.pagination > .active > span {
    background-color: #6c5ce7;
    border-color: #6c5ce7;
}
