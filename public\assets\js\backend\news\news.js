define(['jquery', 'bootstrap', 'backend', 'table', 'form'], function ($, undefined, Backend, Table, Form) {

    var Controller = {
        index: function () {
            // Initialize the Table
            Table.api.init({
                extend: {
                    index_url: 'news/news/index',
                    add_url: 'news/news/add',
                    edit_url: 'news/news/edit',
                    del_url: 'news/news/del',
                    settop_url: 'news/news/settop',
                    table: 'news',
                }
            });

            var table = $("#table");
            table.bootstrapTable({
                url: $.fn.bootstrapTable.defaults.extend.index_url,
                pk: 'id',
                sortName: 'id',
                columns: [
                    [
                        {checkbox: true},
                        {field: 'id', title: 'ID'},
                        {field: 'title', title: __('Title'), formatter: function(value, row, index) {
                            return '<div class="td-text-overflow" title="' + value + '">' + value + '</div>';
                        }, cellStyle: function() {
                            return {
                                css: {
                                    "max-width": "200px"
                                }
                            }
                        }},
                        {field: 'description', title: __('新闻描述'), formatter: function(value, row, index) {
                            return '<div class="td-text-overflow" title="' + value + '">' + value + '</div>';
                        }, cellStyle: function() {
                            return {
                                css: {
                                    "max-width": "250px"
                                }
                            }
                        }},
                        {field: 'content', title: __('Content'), formatter: function(value, row, index) {
                            return '<div class="td-text-overflow" title="' + value + '">' + value + '</div>';
                        }, cellStyle: function() {
                            return {
                                css: {
                                    "max-width": "350px"
                                }
                            }
                        }},
                        {field: 'views', title: __('浏览次数')},
                        {field: 'comments', title: __('评论数')},
                        {field: 'is_top', title: __('是否置顶'), 
                            formatter: function(value, row, index) {
                                var color = value == 1 ? 'success' : 'gray';
                                var icon = value == 1 ? 'fa fa-toggle-on' : 'fa fa-toggle-off';
                                return '<a href="javascript:;" class="btn-toggle" data-id="' + row.id + '" data-value="' + (value == 1 ? 0 : 1) + '"><i class="fa ' + icon + ' text-' + color + '"></i></a>';
                            },
                            events: {
                                'click .btn-toggle': function(e, value, row, index) {
                                    e.preventDefault();
                                    var that = this;
                                    var id = $(e.currentTarget).data('id');
                                    var newValue = $(e.currentTarget).data('value');
                                    Layer.confirm('确认切换状态？', {icon: 3}, function(index) {
                                        Layer.close(index);
                                        Fast.api.ajax({
                                            url: 'news/news/settop',
                                            data: {
                                                ids: id,
                                                is_top: newValue
                                            }
                                        }, function(data, ret) {
                                            table.bootstrapTable('refresh');
                                        });
                                    });
                                }
                            }
                        },
                        {field: 'status', title: __('状态'), 
                            formatter: function(value, row, index) {
                                var color = value == 1 ? 'success' : 'gray';
                                var icon = value == 1 ? 'fa fa-toggle-on' : 'fa fa-toggle-off';
                                return '<a href="javascript:;" class="btn-toggle" data-id="' + row.id + '" data-value="' + (value == 1 ? 0 : 1) + '"><i class="fa ' + icon + ' text-' + color + '"></i></a>';
                            },
                            searchList: {1: __('显示'), 0: __('不显示')},
                            events: {
                                'click .btn-toggle': function(e, value, row, index) {
                                    e.preventDefault();
                                    var that = this;
                                    var id = $(e.currentTarget).data('id');
                                    var newValue = $(e.currentTarget).data('value');
                                    Layer.confirm('确认切换状态？', {icon: 3}, function(index) {
                                        Layer.close(index);
                                        Fast.api.ajax({
                                            url: 'news/news/setstatus',
                                            data: {
                                                ids: id,
                                                status: newValue
                                            }
                                        }, function(data, ret) {
                                            table.bootstrapTable('refresh');
                                        });
                                    });
                                }
                            }
                        },
                        {field: 'is_recommend', title: __('是否推荐'), 
                            formatter: function(value, row, index) {
                                var color = value == 1 ? 'success' : 'gray';
                                var icon = value == 1 ? 'fa fa-toggle-on' : 'fa fa-toggle-off';
                                return '<a href="javascript:;" class="btn-toggle" data-id="' + row.id + '" data-value="' + (value == 1 ? 0 : 1) + '"><i class="fa ' + icon + ' text-' + color + '"></i></a>';
                            },
                            events: {
                                'click .btn-toggle': function(e, value, row, index) {
                                    e.preventDefault();
                                    var that = this;
                                    var id = $(e.currentTarget).data('id');
                                    var newValue = $(e.currentTarget).data('value');
                                    Layer.confirm('确认切换推荐状态？', {icon: 3}, function(index) {
                                        Layer.close(index);
                                        Fast.api.ajax({
                                            url: 'news/news/setrecommend',
                                            data: {
                                                ids: id,
                                                is_recommend: newValue
                                            }
                                        }, function(data, ret) {
                                            table.bootstrapTable('refresh');
                                        });
                                    });
                                }
                            }
                        },
                        {field: 'publishtime', title: __('发布时间'), formatter: Table.api.formatter.datetime},
                        {field: 'createtime', title: __('Createtime'), formatter: Table.api.formatter.datetime},
                        {field: 'operate', title: __('Operate'), table: table, events: Table.api.events.operate, formatter: Table.api.formatter.operate}
                    ]
                ]
            });

            // Common search handling
            Table.api.bindevent(table);

            // 添加自定义CSS样式
            if (!document.getElementById('news-custom-styles')) {
                var style = document.createElement('style');
                style.id = 'news-custom-styles';
                style.type = 'text/css';
                style.innerHTML = `
                    .td-text-overflow {
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        cursor: pointer;
                        display: block;
                        max-width: 100%;
                    }
                    .td-text-overflow:hover {
                        white-space: normal;
                        overflow: visible;
                        background-color: #fff;
                        position: absolute;
                        z-index: 999;
                        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
                        padding: 8px;
                        border-radius: 4px;
                        min-width: 200px;
                        max-width: 500px;
                        min-height: 50px;
                        max-height: 300px;
                        overflow-y: auto;
                        border: 1px solid #ddd;
                        word-break: break-all;
                        word-wrap: break-word;
                    }
                    /* 新增开关按钮样式 */
                    .btn-toggle {
                        text-decoration: none !important;
                    }
                    .btn-toggle i {
                        font-size: 24px;
                        transition: all 0.3s ease;
                    }
                    .btn-toggle i.fa-toggle-on {
                        color: #1abc9c !important;
                    }
                    .btn-toggle i.fa-toggle-off {
                        color: #95a5a6 !important;
                    }
                    .btn-toggle:hover i.fa-toggle-on {
                        color: #16a085 !important;
                        transform: scale(1.1);
                    }
                    .btn-toggle:hover i.fa-toggle-off {
                        color: #7f8c8d !important;
                        transform: scale(1.1);
                    }
                `;
                document.head.appendChild(style);
            }
        },
        add: function () {
            Controller.api.bindevent();
        },
        edit: function () {
            Controller.api.bindevent();
        },
        api: {
            bindevent: function () {
                Form.api.bindevent($("form[role=form]"));
            }
        }
    };
    return Controller;
}); 